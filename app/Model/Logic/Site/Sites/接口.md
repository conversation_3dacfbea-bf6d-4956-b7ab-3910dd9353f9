### 基础信息

- **Base URL**: `https://rapi.auluckypp.com`
- **统一请求头**:
  - **content-type**: `application/json`
  - **Authorization**: 登录后返回的 `token` 值（仅鉴权接口必传）
  - 建议补充：
    - **user-agent**: 浏览器 UA（否则部分接口会返回纯文本 `Go away, robot.`）
    - **accept**: `application/json`
- **统一响应包装**（结合真实联调样本）：
  - **code**: `0|1|401|403`（`1`=成功，`0`=失败，`401`=未授权/需登录，`403`=账号不允许）
  - **message**: `string`（提示信息，如“操作成功”“请重新登录”“参数校验失败...”）
  - **data**: `null | object | array`（业务数据）
  - **status**: `success | fail`

引用来源：[接口域名 `rapi.auluckypp.com`](https://rapi.auluckypp.com/)

---

### 登录/鉴权

- **是否鉴权**: 登录接口无需 `Authorization`，其余“账号相关/下单/玩法配置/订单”等需要。
- **密码加密**: 明文密码需 MD5 32 位小写后传入。
  - 示例（明文 `Aa12345#` 的 MD5）: `7d7e5c6c35c288375c76f33322acae9b`
  - 示例（明文 `Ab123@` 的 MD5）: `d19bc3e3c70a0f81f9af8da3bb00005e`（来源于旧版说明）
  - 本次实测（明文 `Qwe123@` 的 MD5）: `06ea7f9aa1f54b81a599b02306042c91`

#### 登录接口

- **方法**: POST
- **URL**: `/robcenter/author/login`
- **请求体**:
  - `userName` `string` 必填
  - `password` `string` 必填（MD5 32 位小写）
  - `channel` `string` 必填（渠道码）
- **请求示例**

```bash
curl --http1.1 -X POST 'https://rapi.auluckypp.com/robcenter/author/login' \
  -H 'content-type: application/json' \
  -d '{
    "userName": "mmm009",
    "password": "7d7e5c6c35c288375c76f33322acae9b",
    "channel":  "<向上级申请的渠道码>"
  }'
```

- **错误响应示例（实测）**

  - 缺少 `channel`：

  ```json
  { "code": 0, "message": "参数校验失败，字段channel required", "data": "", "status": "fail" }
  ```

  - `channel` 不正确：

  ```json
  { "code": 0, "message": "渠道码不正确", "data": null, "status": "fail" }
  ```

- **使用渠道码 EY96 的登录实测**

  本次联调按你提供的账号 `mmm009` 与明文口令 `Aa12345#`（MD5: `7d7e5c6c35c288375c76f33322acae9b`）尝试登录：

  ```bash
  curl --http1.1 -s -X POST 'https://rapi.auluckypp.com/robcenter/author/login' \
    -H 'content-type: application/json' \
    -d '{
      "userName": "mmm009",
      "password": "7d7e5c6c35c288375c76f33322acae9b",
      "channel":  "EY96"
    }'
  ```

  实际返回（实测）：

  ```json
  { "code": 0, "message": "渠道码不正确", "data": null, "status": "fail" }
  ```

  说明：当前渠道码 `EY96` 在目标环境未生效或未开通，需向渠道方确认有效渠道码以获取 `token`。

- **成功响应（预期）**
  - 字段：
    - `data.token` `string` 登录后用于 `Authorization` 的 token 值
    - 可能包含 `expireAt`、`user` 等信息（以实际返回为准）
  - 形如：
  ```json
  { "code": 1, "message": "操作成功", "data": { "token": "<token>" }, "status": "success" }
  ```

#### 使用渠道码 DK05 的登录实测（成功）

- 方法: POST
- URL: `/robcenter/author/login`
- 请求示例（账号 `qqq1133`，口令明文 `Qwe123@` → MD5 后传入，渠道 `DK05`）：

```bash
curl --http1.1 -s -X POST 'https://rapi.auluckypp.com/robcenter/author/login' \
  -H 'content-type: application/json' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125 Safari/537.36' \
  -H 'accept: application/json' \
  -d '{
    "userName": "qqq1133",
    "password": "06ea7f9aa1f54b81a599b02306042c91",
    "channel":  "DK05"
  }'
```

- 成功响应（节选，实际 `token` 已打码）：

```json
{
  "code": 1,
  "message": "登录成功",
  "data": { "token": "eyJhbGciOi...IXNw", "expired": 1755072501 },
  "status": "success"
}
```

---

### 历史与期信息

以下接口无需携带 `Authorization`（实测）：

#### 当前期信息

- **方法**: POST
- **URL**: `/robcenter/history/current`
- **请求体**: `{}`
- **响应示例（实测）**

```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "running": "ON", // 运行状态 ON|OFF
    "opening": "ON", // 开盘状态 ON|OFF
    "interval": 0, // 剩余或间隔秒数（含义以业务为准）
    "currPid": 51234757, // 当前期号（可能为即将开盘/正在进行期）
    "periodId": 51234756, // 上一期或当前期号（与 currPid 对应关系以业务为准）
    "openTs": 1754628820, // 时间戳（秒）
    "codeNo": "0,1,0,3,2" // 最近开奖号字符串（逗号分隔）
  },
  "status": "success"
}
```

- **字段分析**
  - `running/opening`: `ON|OFF` 字符串枚举，分别表示系统运行与开盘状态
  - `interval`: `number` 间隔/倒计时，单位秒（以实际业务为准）
  - `currPid/periodId`: `number` 期号，二者可能分别表示当前与上一期（以业务实际为准）
  - `openTs`: `number` 秒级时间戳
  - `codeNo`: `string` 逗号分隔的开奖号码

#### 最近 100 期结果

- **方法**: POST
- **URL**: `/robcenter/history/top`
- **请求体**: `{}`
- **响应示例（实测，节选）**

```json
{
  "code": 1,
  "message": "操作成功",
  "data": {
    "date": "2025/08/08",
    "list": [
      {
        "issue": 51234756,
        "nums": ["0", "1", "0", "3", "2"],
        "time": "2025-08-08 12:53:40",
        "next": "2025-08-08 12:58:40"
      }
      // ... 共计约100条
    ],
    "current": {
      "issue": 51234757,
      "time": "2025-08-08 12:58:40",
      "interval": 0,
      "opentime": 1754629120
    }
  },
  "status": "success"
}
```

- **字段分析**
  - `data.date`: `string` 日期，格式 `YYYY/MM/DD`
  - `data.list[]`: 最近期次数组
    - `issue`: `number` 期号
    - `nums`: `string[]` 开奖号码，长度 5，元素为`"0"-"9"`
    - `time`: `string` 开奖时间 `YYYY-MM-DD HH:mm:ss`
    - `next`: `string` 下一开奖时间
  - `data.current`: 当前期信息（与 `/history/current` 字段含义一致/相近）。注意 `opentime` 字段命名为小写。

---

### 玩法与赔率（鉴权）

以下接口需在请求头携带 `Authorization: <token>`。另外，务必补充浏览器 UA 与 `accept: application/json`，否则可能返回 `Go away, robot.`（反爬）。以下为携带上述请求头后的成功实测节选：

#### 大类信息（实测成功）

- **方法**: POST
- **URL**: `/robcenter/game/category`
- **请求体**: `{}`
- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "操作成功",
  "data": [
    {
      "ruleCode": "101100",
      "ruleName": "口XXXX",
      "tradeCode": "101",
      "tradeName": "一字定",
      "limitUpper": "5000.00",
      "limitLower": "1.00",
      "aloneLimit": "5000.00",
      "oddsLimit": "10.0000",
      "increNumber": "1.00"
    },
    {
      "ruleCode": "102100",
      "ruleName": "口口XXX",
      "tradeCode": "102",
      "tradeName": "二字定",
      "limitUpper": "1000.00",
      "limitLower": "1.00",
      "aloneLimit": "1000.00",
      "oddsLimit": "100.0000",
      "increNumber": "1.00"
    },
    { "ruleCode": "103100", "tradeCode": "103", "tradeName": "三字定" },
    { "ruleCode": "104100", "tradeCode": "104", "tradeName": "四字定" },
    { "ruleCode": "105100", "tradeCode": "105", "tradeName": "番摊" }
  ],
  "status": "success"
}
```

#### 子项信息（实测成功）

- **方法**: POST
- **URL**: `/robcenter/game/subitem`
- **请求体**:
  - `tradeCode` `string` 必填（`101`:一定;`102`:二定;`103`:三定;`104`:四定;`105`:番摊）
- 成功请求示例：`{ "tradeCode": "101" }`
- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "操作成功",
  "data": [
    { "ruleCode": "101100", "itemCode": "10110000000", "itemName": "0XXXX", "itemOdds": "10.0000" },
    { "ruleCode": "101101", "itemCode": "10110102000", "itemName": "X2XXX", "itemOdds": "10.0000" },
    { "ruleCode": "101104", "itemCode": "10110400009", "itemName": "XXXX9", "itemOdds": "10.0000" }
  ],
  "status": "success"
}
```

> 特殊说明（沿用旧版文档约定）：
>
> - 一定有效子项分类（`ruleCode`）取值：`101100-101104`
> - 二定有效子项分类（`ruleCode`）取值：`102100-101109`
> - 三定有效子项分类（`ruleCode`）取值：`103100`、`103106`、`103109`（即是前三、中三，后三）
> - 四定有效子项分类（`ruleCode`）取值：`104100`（即是前四）

#### 子项号码映射（实测成功）

- **方法**: POST
- **URL**: `/robcenter/game/codemap`
- **请求体**:
  - `tradeCode` `string` 可选（同上枚举）
- 成功请求示例：`{ "tradeCode": "101" }`
- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "操作成功",
  "data": [
    { "tradeCode": "101", "itemCode": "10110102000", "itemName": "X2XXX" },
    { "tradeCode": "101", "itemCode": "10110300090", "itemName": "XXX9X" },
    { "tradeCode": "101", "itemCode": "10110400009", "itemName": "XXXX9" }
  ],
  "status": "success"
}
```

#### 赔率信息（实测成功）

- **方法**: POST
- **URL**: `/robcenter/game/odds`
- **请求体**: `{}`
- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "操作成功",
  "data": [
    { "ruleCode": "101100", "tradeCode": "101", "itemOdds": "9.99" },
    { "ruleCode": "102100", "tradeCode": "102", "itemOdds": "99.9" },
    { "ruleCode": "103100", "tradeCode": "103", "itemOdds": "999" },
    { "ruleCode": "104100", "tradeCode": "104", "itemOdds": "9990" },
    { "ruleCode": "105100", "tradeCode": "105", "itemOdds": "4" }
  ],
  "status": "success"
}
```

---

### 账户与订单（鉴权）

以下接口需在请求头携带 `Authorization: <token>`：

#### 查看余额（实测成功）

- **方法**: POST
- **URL**: `/robcenter/account/wallet`
- **请求体**: `{}`
- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "操作成功",
  "data": { "userId": "*********", "userName": "qqq1133", "validCredit": "10000", "walletType": 1 },
  "status": "success"
}
```

#### 订单列表（实测成功）

- **方法**: POST
- **URL**: `/robcenter/order/list`
- **请求体**:
  - `page` `number` 必填
  - `size` `number` 必填（默认 10）
- 成功响应（节选）：

```json
{ "code": 1, "message": "操作成功", "data": { "count": 0, "rows": [] }, "status": "success" }
```

#### 订单汇总（实测成功）

- **方法**: POST
- **URL**: `/robcenter/order/count`
- **请求体**: `{}`
- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "操作成功",
  "data": { "periodId": "********", "totalAmt": "0.00" },
  "status": "success"
}
```

---

### 下单/退单（鉴权）

#### 下单（实测成功）

- **方法**: POST
- **URL**: `/robcenter/board/unity`
- **请求体**:
  - `mpList` `string(JSON)` 必填，形如 `[{"label":"10110102000","value":10},{"label":"10110200300","value":10}]`
    - `label`: 号码对应编号
    - `value`: 金额，单位“元”
  - `source` `number` 必填（来源类型，示例可传 `12|13|14|15` 任一）
  - `tagId` `string` 必填（批次编号，退单用）
  - `ltn` `string|number` 必填（当前期数/期号）
- 成功请求示例（按最小 1 元验证，期号请以 `/history/current` 实时返回为准）：

```bash
curl --http1.1 -s -X POST 'https://rapi.auluckypp.com/robcenter/board/unity' \
  -H 'content-type: application/json' \
  -H 'accept: application/json' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125 Safari/537.36' \
  -H 'Authorization: <token>' \
  -d '{
    "mpList":"[{\\"label\\":\\"10110102000\\",\\"value\\":1}]",
    "source":15,
    "tagId":"TAG-<timestamp>",
    "ltn":"********"
  }'
```

- 成功响应（节选）：

```json
{
  "code": 1,
  "message": "报单成功",
  "data": {
    "periodId": "********",
    "totalAmt": "1.00",
    "validCredit": "9999.00",
    "tagId": "TAG-<timestamp>"
  },
  "status": "success"
}
```

#### 退单（实测成功）

- **方法**: POST
- **URL**: `/robcenter/board/cancel`
- **请求体**:
  - `tagId` `string` 必填（批次编号）
- 成功请求示例：`{ "tagId": "TAG-<timestamp>" }`
- 成功响应（节选）：

```json
{ "code": 1, "message": "退单成功", "data": null, "status": "success" }
```

---

### 请求头与示例

- **鉴权请求示例**（需先登录，取到 `token`）：

```bash
curl --http1.1 -X POST 'https://rapi.auluckypp.com/robcenter/game/category' \
  -H 'content-type: application/json' \
  -H 'accept: application/json' \
  -H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125 Safari/537.36' \
  -H 'Authorization: <token>' \
  -d '{}'
```

- **常见未授权响应（实测统一）**

```json
{ "code": 401, "message": "请重新登录", "data": null, "status": "fail" }
```

---

### 错误码与约定

- **code**
  - `1`: 成功
  - `0`: 业务/参数校验失败（如缺少必填字段、`channel` 错误等）
  - `401`: 未授权/登录态失效
  - `403`: 账号不允许/无权限
- **status**
  - `success` | `fail`（与 `code` 含义一致）
- **message**
  - 明确的人类可读提示

---

### 实测要点与排障

- 目标站存在基础反爬保护。建议携带浏览器 UA，并使用 `--http1.1` 发起请求（已在历史/当前期接口实测成功）。
- 若不带浏览器 UA 或 `accept: application/json`，多个鉴权接口会返回纯文本 `Go away, robot.`。补齐上述请求头后可正常返回 JSON。
- 登录必须携带正确 `channel`。若无可用渠道码，将无法获取 `token` 从而无法调用鉴权接口。
- `Authorization` 直接传 `token` 字符串（无 `Bearer ` 前缀）。

---

### 环境与联调建议

- 先在测试环境联调完成（通常无需过白）。
- 正式环境需向渠道方申请域名与 IP 白名单，并获取有效 `channel`。
- 当前实测 `EY96` 渠道码返回“渠道码不正确”，请向渠道确认是否已开通或提供正确渠道码。
