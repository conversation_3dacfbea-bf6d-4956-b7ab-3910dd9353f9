<?php

declare(strict_types=1);

namespace App\Model\Logic\Site;

use Swoft\Db\DB;
use Swoft\Redis\Redis;
use App\Model\Logic\Game\GameLogic;
use App\Model\Logic\Group\ChatLogic;
use App\Model\Logic\Group\GroupLogic;
use App\Model\Logic\Site\Sites\WanHe;
use App\Model\Logic\Site\Sites\FengChe;
use App\Model\Logic\Site\Sites\XingYun;
use App\Model\Logic\Site\Sites\ZiDingYi;
use App\Model\Logic\Site\Sites\ZongHe;
use App\Model\Logic\Site\Sites\QiLing;
use App\Model\Logic\Site\Sites\V98SG;
use App\Model\Logic\Site\Sites\XinLeCheng;
use Exception;

class SiteLogic
{

  public const WanHe  = 'wanhe';
  public const FengChe = 'fengche';
  public const XingYun = 'xingyun';
  public const ZiDingYi = 'zidingyi';
  public const ZongHe = 'zonghe';
  public const QiLing = 'qiling';
  public const V98SG = 'v98sg';
  public const XinLeCheng = 'xinlecheng';


  /**
   * 网盘信息
   */
  public static function siteInfos($game_code, $game_auto)
  {
    $site_infos =  [
      ['site_code' => self::XingYun, 'site_index' => 1, 'site_name' => '555单边一', 'site_bian' => 1, 'valicode' => 0],
      ['site_code' => self::XingYun, 'site_index' => 2, 'site_name' => '555双边一', 'site_bian' => 0, 'valicode' => 0],
      ['site_code' => self::FengChe, 'site_index' => 3, 'site_name' => '555单边二', 'site_bian' => 1, 'valicode' => 0],
      ['site_code' => self::FengChe, 'site_index' => 4, 'site_name' => '555双边二', 'site_bian' => 0, 'valicode' => 0],
      ['site_code' => self::WanHe, 'site_index' => 5, 'site_name' => '万合', 'site_bian' => 0, 'valicode' => 0],
      ['site_code' => self::ZiDingYi, 'site_index' => 6, 'site_name' => 'UB自定义', 'site_bian' => 0, 'valicode' => 1],
      ['site_code' => self::ZongHe, 'site_index' => 7, 'site_name' => '综合', 'site_bian' => 0, 'valicode' => 1],
      ['site_code' => self::QiLing, 'site_index' => 8, 'site_name' => '麒麟单边一', 'site_bian' => 1, 'valicode' => 0],
      ['site_code' => self::QiLing, 'site_index' => 9, 'site_name' => '麒麟双边一', 'site_bian' => 0, 'valicode' => 0],
      ['site_code' => self::V98SG, 'site_index' => 10, 'site_name' => 'V98', 'site_bian' => 0, 'valicode' => 0],
      ['site_code' => self::XinLeCheng, 'site_index' => 11, 'site_name' => "新乐城单边", 'site_bian' => 1, 'valicode' => 0],
      ['site_code' => self::XinLeCheng, 'site_index' => 12, 'site_name' => "新乐城双边", 'site_bian' => 0, 'valicode' => 0],
    ];
    switch (true) {
      case $game_code == 'azxy5_ft' && $game_auto == 0:
        $site_indexs = [1, 2, 3, 4, 8, 9, 11, 12];
        break;
      case $game_code == 'azxy5_ft' && $game_auto == 1:
        $site_indexs = [1, 2, 3, 4, 5, 6, 10, 11, 12];
        break;
      case $game_code == 'azxy5_dw' && $game_auto == 0:
        $site_indexs = [3, 4, 5, 6, 7];
        break;
      case $game_code == 'azxy8_ft' && $game_auto == 0:
        $site_indexs = [1, 2, 3, 4, 8, 9, 10, 11, 12];
        break;
      case $game_code == 'azxy8_ts' && $game_auto == 0:
        $site_indexs = [1, 2, 3, 4, 8, 9, 10, 11, 12];
        break;
      default:
        $site_indexs = [1, 2, 3, 4, 5];
        break;
    }
    $game_site = [];
    foreach ($site_infos as $site_info) {
      if (in_array($site_info['site_index'], $site_indexs)) {
        $game_site[] = $site_info;
      }
    }
    return $game_site;
  }

  /**
   * 处理登陆
   */
  public static function handleLogin($params)
  {
    switch ($params['site_code']) {
      case self::WanHe:
        return WanHe::handleLogin($params);

      case self::FengChe:
        return FengChe::handleLogin($params);

      case self::XingYun:
        return XingYun::handleLogin($params);

      case self::ZiDingYi:
        return ZiDingYi::handleLogin($params);

      case self::ZongHe:
        return ZongHe::handleLogin($params);

      case self::QiLing:
        return QiLing::handleLogin($params);

      case self::V98SG:
        return V98SG::handleLogin($params);

      case self::XinLeCheng:
        return XinLeCheng::handleLogin($params);
      default:
        return ['code' => -1, 'message' => '当前网盘不可用', 'data' => []];
    }
  }

  /**
   * 处理登陆
   */
  public static function handleValiCode($params)
  {
    extract($params);
    switch ($site_code) {
      case self::ZiDingYi:
        return ZiDingYi::valiCode($domain, $checkcode);

      case self::ZongHe:
        return ZongHe::valiCode($domain, $checkcode);

      default:
        return ['code' => -1, 'message' => '当前网盘无验证码', 'data' => []];
    }
  }

  /**
   * 刷新网盘信息
   */
  public static function refreshWP()
  {

    $pan_logins = Redis::hGetAll('pan_login');
    if (!$pan_logins) return;
    foreach ($pan_logins as $group_id => $pan_login) {
      sgo(function () use ($group_id, $pan_login) {
        extract($pan_login);
        switch ($site_code) {
          case self::WanHe:
            $refresh = WanHe::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::FengChe:
            $refresh = FengChe::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::XingYun:
            $refresh = XingYun::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::ZiDingYi:
            $refresh = ZiDingYi::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::ZongHe:
            $refresh = ZongHe::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::QiLing:
            $refresh = QiLing::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::V98SG:
            $refresh = V98SG::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          case self::XinLeCheng:
            $refresh = XinLeCheng::refreshInfo($domain, $checkcode, $cookie, $uid, $group_id, $game_code, $game_auto);
            break;

          default:
            return;
        }
        if ($refresh) {
          $retry = 0;
        } else {
          if ($retry >= 3) {
            self::handleLogout($group_id);
          } else {
            $retry += 1;
          }
        }
        $pan_login['retry'] = $retry;
        Redis::hSet('pan_login', strval($group_id), $pan_login);
      });
    }
  }



  //退出网盘
  public static function handleLogout($group_id)
  {
    Redis::hDel('pan_login', strval($group_id));
    Redis::hDel('pan_infos', strval($group_id));
    DB::table('group')->where('id', $group_id)->update(['is_upload_wp' => 0]);
  }

  public static function autoCancleWP()
  {
    try {
      $groups = DB::table('group')
        ->where('is_upload_wp', 0)
        ->where('auto_cancel', 1)
        ->where('status', 1)
        ->get(['id', 'game_code', 'delayupload'])
        ->toArray();
      foreach ($groups as $group) {
        sgo(function () use ($group) {
          [
            'id'          => $group_id,
            'game_code'   => $game_code,
            'delayupload' => $delay,
          ] = $group;
          $qihao = GameLogic::getGameDraw($game_code);
          $delay_time = date('Y-m-d H:i:s', strtotime("-{$delay} seconds"));
          $order_rows = DB::table('game_order')
            ->where('group_id', $group_id)
            ->where('qihao', $qihao)
            ->where('sxfen', 0)
            ->where('pk_status', '<>', 1)
            ->where('is_robot', 0)
            ->where('create_time', '<=', $delay_time)
            ->get(['id', 'content'])
            ->toArray();
          $order_ids  = array_column($order_rows, 'id');
          self::setCancelOrder($order_ids);
        });
      }
    } catch (\Exception $e) {
      vdump($e->getMessage(), $e->getLine());
    }
  }

  public static function autoSubmitWP()
  {
    try {
      if (Redis::exists('pan_login') == false) {
        return;
      }
      //获取登陆网盘的房间id
      $pan_logins = Redis::hGetAll('pan_login');
      if (!$pan_logins) {
        return;
      }
      foreach ($pan_logins as $group_id => $pan_login) {
        $is_upload_wp = DB::table('group')->where('id', $group_id)->value('is_upload_wp');
        if ($is_upload_wp != 0) {
          $lock_key = "order_lock_{$group_id}";
          $lock_val = "order_lock_{$group_id}" . uniqid();
          sgo(function () use ($group_id, $pan_login, $lock_key, $lock_val) {
            try {
              $is_lock  = Redis::set($lock_key, $lock_val, ['nx', 'ex' => 4]);
              if ($is_lock) {
                [
                  'game_code' => $game_code,
                  'game_auto' => $game_auto,
                  'site_code' => $site_code,
                  'domain'    => $domain,
                  'checkcode' => $checkcode,
                  'cookie'    => $cookie,
                  'uid'       => $uid,
                  'bian'      => $bian,
                  'delay'     => $delay,
                  'cancel'    => $cancel
                ] = $pan_login;
                $qihao = GameLogic::getGameDraw($game_code);
                $delay_time = date('Y-m-d H:i:s', strtotime("-{$delay} seconds"));
                $order_rows = DB::table('game_order')
                  ->where('qihao', $qihao)
                  ->where('group_id', $group_id)
                  ->where('pk_status', 0)
                  ->where('is_robot', 0)
                  ->where('sxfen', 0)
                  ->where('create_time', '<=', $delay_time)
                  ->get(['id', 'content'])
                  ->toArray();
                if (!$order_rows) return;
                if (Redis::get($lock_key) == $lock_val) {
                  $order_ids  = array_column($order_rows, 'id');
                  $order_strs = array_column($order_rows, 'content');
                  self::setWaitOrder($order_ids);
                  switch ($site_code) {
                    case self::FengChe:
                      $result = FengChe::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::XingYun:
                      $result = XingYun::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::WanHe:
                      $result = WanHe::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::ZiDingYi:
                      $result = ZiDingYi::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::ZongHe:
                      $result = ZongHe::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::QiLing:
                      $result = QiLing::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::V98SG:
                      $result = V98SG::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    case self::XinLeCheng:
                      $result = XinLeCheng::submitOrder($domain, $checkcode, $cookie, $uid, $qihao, $order_ids, $order_strs, $bian, $game_code, $game_auto, $group_id);
                      break;

                    default:
                      return;
                  }
                  if ($result['ok']) {
                    self::setSuccessOrder($result['ok']);
                  }
                  if ($result['no']) {
                    if ($cancel == 1) {
                      self::setCancelOrder($result['no']);
                    } else {
                      self::setFailOrder($result['no']);
                    }
                  }
                  Redis::del($lock_key);
                }
              }
            } catch (\Exception $e) {
              vdump($e->getMessage(), $e->getLine());
            }
          });
        }
      }
    } catch (\Exception $e) {
      vdump($e->getMessage(), $e->getLine());
    }
  }

  public static function setWaitOrder($order_ids)
  {
    return DB::table('game_order')->whereIn('id', $order_ids)->update(['pk_status' => 3]);
  }

  public static function setSuccessOrder($order_ids)
  {
    return DB::table('game_order')->whereIn('id', $order_ids)->update(['pk_status' => 1]);
  }


  public static function setFailOrder($order_ids)
  {
    return DB::table('game_order')->whereIn('id', $order_ids)->update(['pk_status' => 2]);
  }

  public static function setCancelOrder($order_ids)
  {

    DB::beginTransaction();
    try {
      $orders  = DB::table('game_order')
        ->whereIn('id', $order_ids)
        ->get(['group_id', 'user_id', 'content', 'order_points'])
        ->toArray();
      $results = [];
      foreach ($orders as $order) {
        extract($order);
        if (isset($results[$user_id])) {
          $results[$user_id]['points']  += $order_points;
          $results[$user_id]['content'] .= ",{$content}";
        } else {
          $results[$user_id]['points']  = $order_points;
          $results[$user_id]['content'] = "{$content}";
          $results[$user_id]['detail']  = GroupLogic::getGroupUserDetail($group_id, $user_id);
        }
      }
      foreach ($results as $result) {
        extract($result);
        $group_id = $detail['group_id'];
        $user_id  = $detail['user_id'];
        $user_points = DB::table('user_points')
          ->where('group_id', $group_id)
          ->where('user_id', $user_id)
          ->first(['id', 'points']);
        $old_points = $user_points['points'];
        $now_points = $old_points + $points;
        //更新取消积分
        DB::table('user_points')->where('id', $user_points['id'])->update(['points' => $now_points]);

        $message = "入网失败，取消“{$content}”，共：{$points} 已退回，剩：{$now_points}";
        ChatLogic::robotReply($group_id, $message, $detail);
      }
      DB::table('game_order')->whereIn('id', $order_ids)->delete();
      DB::commit();
    } catch (\Exception $e) {
      vdump($e->getMessage(), $e->getLine());
      DB::rollBack();
    }
  }


  //订单标记
  public static function orderMarkStatus($status)
  {
    switch (true) {
      case $status == 3:
        return 1;
      case $status == 1:
        return 2;
      case $status == 2:
        return 3;
      default:
        return 0;
    }
  }
}
